{"path": "backend/src/test_doc_feature.py", "contentHash": "3e3a66140441dbddd8b708d82a6ac284981a5ee2493746302dd77a5f70b3c573", "commit": "afa4561c7dcd49edaad94870a915728542528d1e", "timestamp": "2025-07-01T18:11:05+05:30", "units": [{"unitName": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "unitType": "function", "purpose": "This function calculates the nth <PERSON><PERSON><PERSON><PERSON> number using dynamic programming.", "humanReadableExplanation": "The `calculate_fibonacci` function computes the nth Fibonacci number efficiently using dynamic programming. It first handles base cases where n is 0 or 1. For n greater than 1, it initializes a list `fib` with the first two Fibonacci numbers (0 and 1). Then, it iteratively calculates subsequent Fibonacci numbers by adding the two preceding numbers in the `fib` list and appending the result. Finally, it returns the nth Fibonacci number from the `fib` list.  The function raises a ValueError if the input `n` is negative.", "dependencies": [], "inputs": [{"name": "n", "type": "int", "description": "A positive integer representing which <PERSON><PERSON><PERSON><PERSON> number to calculate"}], "outputs": {"type": "int", "description": "The nth <PERSON><PERSON><PERSON><PERSON> number", "throws": ["ValueError"]}, "visualDiagram": "graph TD\n    A[Start];\n    B{n < 0?};\n    C[Raise ValueError];\n    D{n <= 1?};\n    E[Return n];\n    F[Initialize fib = [0, 1]];\n    G{i in range(2, n+1)?};\n    H[fib.append(fib[i-1] + fib[i-2])];\n    I[Return fib[n]];\n    A --> B;\n    B -- Yes --> C;\n    B -- No --> D;\n    D -- Yes --> E;\n    D -- No --> F;\n    F --> G;\n    G -- Yes --> H;\n    G -- No --> I;\n    H --> G;\n    C --> A;\n    E --> A;\n    I --> A;"}, {"unitName": "DocumentationTester", "unitType": "class", "purpose": "The `DocumentationTester` class simulates running tests and provides a summary of the tests executed.", "humanReadableExplanation": "This Python class `DocumentationTester` is designed to demonstrate how to document classes and methods effectively.  It simulates a testing environment. The `__init__` method initializes an instance of the class with a name and sets an initial test count to zero. The `run_test` method increments the test count and prints a message indicating a test is running.  It always returns `True`, simulating a successful test.  The `get_test_summary` method returns a dictionary containing the tester's name and the total number of tests run. This class is primarily for showcasing documentation generation, not for actual testing.", "dependencies": [], "inputs": [{"name": "name", "type": "str", "description": "A string identifier for this tester instance"}, {"name": "test_name", "type": "str", "description": "Name of the test being run"}], "outputs": {"type": "dict", "description": "A dictionary containing test statistics", "throws": []}, "visualDiagram": "classDiagram\n    class DocumentationTester {\n        -name: str\n        -test_count: int\n        +__init__(name: str)\n        +run_test(test_name: str): bool\n        +get_test_summary(): dict\n    }\n"}]}