{"path": "backend/scripts/generate_docs.py", "contentHash": "df9566c9ff13d216efc8bf5a6f86790b105ed1eb132495d9f8dda0ed0ba8d5b3", "commit": "afa4561c7dcd49edaad94870a915728542528d1e", "timestamp": "2025-07-01T18:11:05+05:30", "units": [{"unitName": "log_error", "unitType": "function", "purpose": "The log_error function prints an error message to the standard error stream.", "humanReadableExplanation": "This function takes a message as input and prints it to the standard error stream (sys.stderr).  Standard error is typically used for error messages and warnings to distinguish them from standard output. This ensures that error messages are clearly visible, even if standard output is redirected or processed in some way. The function doesn't return any value.", "dependencies": [], "inputs": [{"name": "message", "type": "str", "description": "The error message to be printed."}], "outputs": {"type": "None", "description": "This function does not return a value.", "throws": []}, "visualDiagram": "graph TD\n    A[log_error function] --> B{message};\n    B --> C[print to sys.stderr];"}, {"unitName": "log_info", "unitType": "function", "purpose": "The log_info function prints informational messages to the standard error stream.", "humanReadableExplanation": "This function takes a message as input and prints it to the standard error stream (sys.stderr).  Standard error is typically used for logging informational, warning, or error messages, separating them from the standard output (stdout) which is usually reserved for program results or user interaction.  This separation helps in debugging and monitoring the application's behavior. The `file=sys.stderr` argument explicitly directs the output to the standard error stream.", "dependencies": [], "inputs": [{"name": "message", "type": "str", "description": "The message to be printed to the standard error stream."}], "outputs": {"type": "None", "description": "This function does not return any value.", "throws": []}, "visualDiagram": "graph TD\n    A[log_info function] --> B{message};\n    B --> C[print to sys.stderr];\n    C --> D[End];"}, {"unitName": "LLMClient", "unitType": "class", "purpose": "The `LLMClient` class facilitates communication with a large language model (LLM) API, such as Gemini, to generate documentation for code snippets.", "humanReadableExplanation": "This class acts as an interface to an LLM API.  The `__init__` method initializes the client using an API key and model name from environment variables. It raises a `ValueError` if the API key is missing or invalid. The `_build_prompt` method constructs a prompt for the LLM, including the code snippet, file path, and instructions for generating documentation in a specific JSON format. The `generate_documentation` method sends the prompt to the LLM API, handles potential network errors and parsing issues, and returns the generated JSON documentation or `None` if an error occurs.  It uses exponential backoff for retries in case of network errors. The API endpoint is dynamically built using the model name and API key.", "dependencies": [{"type": "external", "name": "requests"}, {"type": "external", "name": "os"}, {"type": "external", "name": "json"}, {"type": "external", "name": "google.generativelanguage"}], "inputs": [], "outputs": {"type": "json", "description": "A JSON object containing the generated documentation for the provided code snippet, or None if there was an error.", "throws": ["requests.exceptions.RequestException", "json.JSONDecodeError", "ValueError", "KeyError", "IndexError"]}, "visualDiagram": "A Mermaid.js 'classDiagram' that visually represents the logic. Be accurate and detailed."}, {"unitName": "get_staged_files", "unitType": "function", "purpose": "The `get_staged_files` function retrieves a list of files that have been staged in the Git repository but not yet committed.", "humanReadableExplanation": "This function interacts with the Git command-line interface (CLI) to identify files marked for the next commit.  It first uses `git rev-parse --show-toplevel` to find the root directory of the Git repository. Then, it changes the current working directory to that root using `os.chdir`.  Next, it executes `git diff --cached --name-only --diff-filter=AM`. This Git command lists only the names of files that are added or modified and are staged for the next commit. The output is a string where each line represents a file path. The function then splits this string into a list of file paths, removing any empty strings, and returns this list.  If any error occurs during the Git commands or if the `git` command is not found, it logs an error message and returns an empty list.", "dependencies": [{"type": "external", "name": "subprocess"}, {"type": "external", "name": "os"}, {"type": "external", "name": "git"}], "inputs": [], "outputs": {"type": "list of strings", "description": "A list of file paths representing the staged files. Returns an empty list if there's an error or no staged files.", "throws": ["subprocess.CalledProcessError", "FileNotFoundError"]}, "visualDiagram": "graph TD\n    A[Start]\n    B{Find Git root with `git rev-parse --show-toplevel`}\n    C[Change directory to Git root]\n    D{Run `git diff --cached --name-only --diff-filter=AM`}\n    E[Split output into list of file paths]\n    F{Error?}\n    G[Log error, return []]\n    H[Return list of staged files]\n    A --> B\n    B --> C\n    C --> D\n    D --> E\n    E --> F\n    F -- Yes --> G\n    F -- No --> H"}, {"unitName": "calculate_hash", "unitType": "function", "purpose": "The `calculate_hash` function computes a SHA-256 hash of an input string.", "humanReadableExplanation": "This function takes a string as input and uses the `hashlib` library to generate its SHA-256 hash.  The input string is first encoded to UTF-8 bytes before hashing. The function then returns the hexadecimal representation of the resulting hash as a string. This is commonly used to create unique identifiers for content or to verify data integrity.", "dependencies": [{"type": "internal", "name": "<PERSON><PERSON><PERSON>"}], "inputs": [{"name": "content", "type": "str", "description": "The string whose hash needs to be calculated."}], "outputs": {"type": "str", "description": "The hexadecimal representation of the SHA-256 hash of the input string.", "throws": []}, "visualDiagram": "'graph TD'\n    A[Input String] --> B{Encode to UTF-8};\n    B --> C[SHA-256 Hashing];\n    C --> D[Hexadecimal Representation];\n    D --> E[Output String];"}, {"unitName": "parse_js_ts_with_helper", "unitType": "function", "purpose": "This function parses JavaScript or TypeScript files using a Node.js helper script or falls back to a regular expression-based parser if the helper fails.", "humanReadableExplanation": "The `parse_js_ts_with_helper` function aims to parse JavaScript and TypeScript files for code analysis. It prioritizes using an external Node.js helper script (`parse_js_ts.ts`) for parsing.  This script is executed via `ts-node`. The function constructs the command to run this script, providing the file path as input. The output of the helper script is expected to be a JSON string, which is then parsed using `json.loads`. However, if any error occurs during the execution of the helper script (e.g., `subprocess.CalledProcessError`, `json.JSONDecodeError`, `FileNotFoundError`), the function gracefully handles the exception. In case of failure, it falls back to a regular expression-based parsing method (`parse_with_regex`), reading the file content directly and attempting to extract relevant information using regular expressions. The function then returns the parsed data, either from the helper script or the regex fallback.", "dependencies": [{"type": "internal", "name": "parse_with_regex"}, {"type": "external", "name": "ts-node"}, {"type": "external", "name": "parse_js_ts.ts"}], "inputs": [{"name": "file_path", "type": "string", "description": "The path to the JavaScript or TypeScript file to be parsed."}], "outputs": {"type": "object", "description": "Returns a JSON object representing the parsed content of the file. The structure of this object depends on the parsing method used (helper script or regex).  If parsing fails completely, it may return None.", "throws": ["subprocess.CalledProcessError", "json.JSONDecodeError", "FileNotFoundError"]}, "visualDiagram": "graph TD\n    A[Start] --> B{Helper Script Execution Successful?};\n    B -- Yes --> C[Parse JSON Output];\n    C --> D[Return Parsed Data];\n    B -- No --> E[Fallback to Regex Parsing];\n    E --> D;\n    D --> F[End];"}, {"unitName": "parse_file_for_units", "unitType": "function", "purpose": "This function determines the appropriate parsing method for a given file based on its extension and parses the file content to extract functions and classes.", "humanReadableExplanation": "The `parse_file_for_units` function acts as a dispatcher for parsing different file types.  It takes the file path and its content as input. If the file ends with '.py', it uses the `parse_python_with_ast` function which leverages Python's Abstract Syntax Tree (AST) for precise parsing of Python code. For '.js', '.ts', and '.tsx' files, it employs the `parse_js_ts_with_helper` function, which likely uses a Node.js helper script for more robust JavaScript/TypeScript parsing.  For all other file types, it falls back to a regular expression-based approach (`parse_with_regex`), which is less precise but more broadly applicable. The function returns a list of parsed units (functions, classes, or a file overview if no functions or classes are found).", "dependencies": [{"type": "internal", "name": "parse_python_with_ast"}, {"type": "internal", "name": "parse_js_ts_with_helper"}, {"type": "internal", "name": "parse_with_regex"}], "inputs": [{"name": "file_path", "type": "string", "description": "The path to the file to be parsed."}, {"name": "content", "type": "string", "description": "The content of the file to be parsed."}], "outputs": {"type": "list", "description": "A list of dictionaries, where each dictionary represents a parsed unit (function, class, or file overview) with its name, type, and content.  Returns an empty list if parsing fails completely.", "throws": []}, "visualDiagram": "graph TD\n    A[parse_file_for_units(file_path, content)] --> |file_path.endswith('.py')| B(parse_python_with_ast);\n    A --> |file_path.endswith('.js','.ts','.tsx')| C(parse_js_ts_with_helper);\n    A --> |otherwise| D(parse_with_regex);\n    B --> E[list of units];\n    C --> E;\n    D --> E;\n    E --> F[return];"}, {"unitName": "parse_python_with_ast", "unitType": "function", "purpose": "The `parse_python_with_ast` function parses Python code using the Abstract Syntax Tree (AST) to extract top-level functions and classes, returning them as a list of dictionaries.", "humanReadableExplanation": "This function takes Python code as input and uses the `ast` module to analyze its structure. It iterates through the top-level elements of the code (functions, classes, etc.). If it finds a function or class definition, it extracts the source code for that unit and stores it in a dictionary along with the unit's name and type ('function' or 'class').  If no functions or classes are found, it returns a dictionary representing the file overview.  If a `SyntaxError` occurs during AST parsing, it falls back to a regular expression-based parsing method (`parse_with_regex`). The function returns a list of dictionaries, where each dictionary represents a function or class found in the input code, or a file overview if no functions or classes are found.", "dependencies": [{"type": "internal", "name": "ast"}, {"type": "internal", "name": "log_error"}, {"type": "internal", "name": "parse_with_regex"}], "inputs": [{"name": "content", "type": "str", "description": "The Python code to be parsed."}], "outputs": {"type": "list", "description": "A list of dictionaries. Each dictionary represents a function or class found, containing its name, type, and source code.  If no functions or classes are found, it contains a single dictionary representing the file overview.", "throws": ["SyntaxError"]}, "visualDiagram": "graph TD\n    A[parse_python_with_ast(content)] --> B{SyntaxError?};\n    B -- Yes --> C[parse_with_regex(content)];\n    B -- No --> D[Iterate through AST nodes];\n    D --> E{Function or Class?};\n    E -- Yes --> F[Extract source code];\n    F --> G[Append to units];\n    E -- No --> H{Units empty?};\n    H -- Yes --> I[Append file overview];\n    G --> J[Return units];\n    I --> J;"}, {"unitName": "parse_with_regex", "unitType": "function", "purpose": "The `parse_with_regex` function extracts functions and classes from a given code string using regular expressions, returning them as a list of units.", "humanReadableExplanation": "This function takes a string containing code as input and uses regular expressions to identify functions and classes within it.  It first defines two regular expressions: `class_pattern` to match class definitions (including decorators) and `func_pattern` to match function definitions (including decorators and async functions).  The function then iterates through the code using these patterns, finding all matches. Each match is stored as a tuple containing the unit type ('class' or 'function'), the unit's name, and the start and end indices of the unit within the input string.  These tuples are sorted by their starting index. Finally, the function constructs a list of dictionaries, where each dictionary represents a unit and contains its name, type, and the extracted code segment. If no functions or classes are found, it returns a single unit representing the entire file. The function returns a list of dictionaries, each representing a function or class found in the input code, or a single dictionary representing the whole file if no functions or classes are found.", "dependencies": [{"type": "internal", "name": "re"}], "inputs": [{"name": "content", "type": "str", "description": "The code string to be parsed."}], "outputs": {"type": "list", "description": "A list of dictionaries. Each dictionary represents a unit (function or class) found in the input code, containing its name, type, and code segment.  If no functions or classes are found, it contains a single dictionary representing the whole file.", "throws": []}, "visualDiagram": "A Mermaid.js 'graph TD' that visually represents the logic. Be accurate and detailed."}, {"unitName": "process_file", "unitType": "function", "purpose": "The `process_file` function reads a file, analyzes its code units (functions or classes), generates documentation for each unit using an LLM, and saves the documentation to a JSON file.", "humanReadableExplanation": "This function is the core of the documentation generation process.  First, it reads the content of a given file. Then, it calculates a SHA256 hash of the file's content to detect changes since the last run. It checks if a documentation file already exists for this file and if the content hash matches; if so, it skips processing to avoid redundant work. Otherwise, it parses the file's content to identify code units (functions or classes) using the `parse_file_for_units` function. For each unit, it calls the `llm_client.generate_documentation` method to obtain enriched documentation from a large language model (LLM).  The generated documentation for each unit is then combined into a single JSON object, including metadata such as the file path, content hash, commit hash, and timestamp. Finally, it creates the necessary directory structure and saves this JSON object to a '.doc.json' file in the `GENERATED_DOCS_BASE_DIR` directory.  Error handling is implemented at multiple stages to gracefully handle file reading issues, LLM API failures, and JSON processing errors.", "dependencies": [{"type": "internal", "name": "calculate_hash"}, {"type": "internal", "name": "parse_file_for_units"}, {"type": "internal", "name": "get_git_info"}, {"type": "internal", "name": "LLMClient"}, {"type": "external", "name": "os"}, {"type": "external", "name": "json"}, {"type": "external", "name": "<PERSON><PERSON><PERSON>"}], "inputs": [{"name": "file_path", "type": "str", "description": "The path to the file to be processed."}, {"name": "llm_client", "type": "LLMClient", "description": "An instance of the LLMClient class, used to interact with the large language model for documentation generation."}], "outputs": {"type": "str", "description": "The path to the generated documentation JSON file, or None if any error occurred during processing.", "throws": ["FileNotFoundError", "IOError", "json.JSONDecodeError", "requests.exceptions.RequestException", "Exception"]}, "visualDiagram": "graph TD\n    A[Start] --> B{Read file};\n    B -- Success --> C[Calculate content hash];\n    B -- Error --> F[Log error, return None];\n    C --> D{Check if doc exists & hash matches};\n    D -- Yes --> F;\n    D -- No --> E[Parse file for units];\n    E --> G{Generate doc for each unit};\n    G -- Success --> H[Create final doc object];\n    G -- Error --> F;\n    H --> I[Save doc to file];\n    I -- Success --> J[Return file path];\n    I -- Error --> F;\n    J --> K[End];\n    F --> K;"}, {"unitName": "get_git_info", "unitType": "function", "purpose": "The `get_git_info` function retrieves the latest commit hash and timestamp from the Git repository.", "humanReadableExplanation": "This function attempts to fetch the most recent commit's hash and timestamp using the `git` command-line tool.  It uses `subprocess.run` to execute the git commands.  `git rev-parse HEAD` gets the commit hash, and `git log -1 --format=%cd --date=iso-strict` gets the timestamp in ISO 8601 format. If any of the `git` commands fail (e.g., not in a Git repository), a `subprocess.CalledProcessError` is caught, and the function returns default values: \"unknown_commit\" for the hash and the current timestamp for the timestamp, formatted as an ISO 8601 string with the 'Z' timezone indicator.", "dependencies": [{"type": "external", "name": "subprocess"}, {"type": "external", "name": "datetime"}], "inputs": [], "outputs": {"type": "tuple", "description": "Returns a tuple containing the commit hash (string) and timestamp (string). If Git commands fail, it returns (\"unknown_commit\", current timestamp).", "throws": ["subprocess.CalledProcessError"]}, "visualDiagram": "graph TD\n    A[Start]\n    B{Git commands successful?}\n    C[Get commit hash and timestamp]\n    D[Return commit hash, timestamp]\n    E[Error handling]\n    F[Return \"unknown_commit\", current timestamp]\n    A --> B\n    B -- Yes --> C\n    C --> D\n    B -- No --> E\n    E --> F"}, {"unitName": "load_config", "unitType": "function", "purpose": "The `load_config` function retrieves configuration settings from a JSON file, providing default values if the file is missing or unreadable.", "humanReadableExplanation": "This function attempts to load configuration settings from a JSON file located at a specific path relative to the script's location.  The path is constructed using `os.path.join` to ensure platform independence. It uses a `try-except` block to handle potential errors: `FileNotFoundError` if the config file doesn't exist and `json.JSONDecodeError` if the file's contents are not valid JSON. If an error occurs, a warning message is logged using the `log_error` function, and a default configuration dictionary is returned. This default dictionary includes lists of allowed file extensions and directories to ignore during processing. If the file is successfully loaded, the function returns the parsed JSON data as a Python dictionary.", "dependencies": [{"type": "internal", "name": "os"}, {"type": "internal", "name": "json"}, {"type": "internal", "name": "log_error"}], "inputs": [], "outputs": {"type": "dict", "description": "Returns a dictionary containing configuration settings.  If the config file is not found or is invalid, it returns a dictionary with default settings.", "throws": ["FileNotFoundError", "json.JSONDecodeError"]}, "visualDiagram": "graph TD\n    A[Start]\n    B{Config file exists?}\n    C[Load config from JSON]\n    D[Return config]\n    E[Handle FileNotFoundError/JSONDecodeError]\n    F[Log warning]\n    G[Return default config]\n    A --> B\n    B -- Yes --> C\n    C --> D\n    B -- No --> E\n    E --> F\n    F --> G"}, {"unitName": "main", "unitType": "function", "purpose": "The main function orchestrates the generation of documentation for staged files in a Git repository.", "humanReadableExplanation": "The `main` function is the entry point of the script. It first loads configuration settings, including allowed file extensions and directories to ignore.  It then attempts to initialize an LLMClient to interact with a large language model for documentation generation. If the LLM client initialization fails (e.g., API key not found), the script exits with an error. Next, it retrieves a list of staged files using Git commands. If no staged files are found, it exits gracefully. The script iterates through each staged file, checking if its extension is allowed and if it's located within the 'backend' directory and not within any ignored directories. If both conditions are true, it calls the `process_file` function to generate documentation for that file.  `process_file` handles reading the file, calculating a content hash to detect changes, and using the LLM client to generate documentation. The generated documentation is saved to a file. Finally, `main` prints the paths of the files for which documentation was successfully generated, or a message indicating that no documentation was updated.", "dependencies": [{"type": "internal", "name": "load_config"}, {"type": "internal", "name": "LLMClient"}, {"type": "internal", "name": "get_staged_files"}, {"type": "internal", "name": "process_file"}, {"type": "external", "name": "Git"}, {"type": "external", "name": "os"}, {"type": "external", "name": "sys"}], "inputs": [], "outputs": {"type": "null", "description": "This function does not return a value. It prints the paths of generated documentation files to standard output or a message indicating no updates.", "throws": []}, "visualDiagram": "graph TD\n    A[Start]\n    B{Load Config};\n    C{Initialize LLMClient};\n    D{Get Staged Files};\n    E{Files Exist?};\n    F[No Files, Exit];\n    G{Iterate Files};\n    H{Allowed Extension & Directory?};\n    I{Process File};\n    J{Save Documentation};\n    K{Print Paths};\n    L[No Updates, Exit];\n    A --> B;\n    B --> C;\n    C -- Success --> D;\n    C -- Failure --> F;\n    D --> E;\n    E -- Yes --> G;\n    E -- No --> F;\n    G --> H;\n    H -- Yes --> I;\n    H -- No --> G;\n    I --> J;\n    J --> K;\n    K --> L;\n    F --> L;"}]}