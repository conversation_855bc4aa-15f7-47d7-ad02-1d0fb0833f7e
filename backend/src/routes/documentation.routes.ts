import { Router } from 'express';
import { generateDocumentation } from '../services/documentation.service';

const router = Router();

router.post('/generate', async (req, res) => {
  try {
    const { projectPath } = req.body;
    if (!projectPath) {
      return res.status(400).send('projectPath is required');
    }
    await generateDocumentation(projectPath);
    res.status(200).send('Documentation generation started');
  } catch (error) {
    console.error('Error generating documentation:', error);
    res.status(500).send('Error generating documentation');
  }
});

export default router;
