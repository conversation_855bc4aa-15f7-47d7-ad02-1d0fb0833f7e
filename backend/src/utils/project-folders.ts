/**
 * Project Folder Management Utility
 * 
 * Handles creation and management of Demo 1 project folder structure:
 * - slides/
 * - mockups/
 * - docs/
 * - test-cases/
 * - code/
 */

import { promises as fs } from 'fs';
import path from 'path';
import { logger } from '../common/logger';

export interface ProjectFolders {
  slides: string;
  mockups: string;
  docs: string;
  testCases: string;
  code: string;
}

export interface ProjectStructure {
  projectPath: string;
  folders: ProjectFolders;
}

/**
 * Create Demo 1 folder structure in the specified project directory
 */
export async function createProjectFolders(projectPath: string): Promise<ProjectStructure> {
  try {
    // Ensure project root exists
    await fs.mkdir(projectPath, { recursive: true });
    
    // Define folder structure
    const folders: ProjectFolders = {
      slides: path.join(projectPath, 'slides'),
      mockups: path.join(projectPath, 'mockups'),
      docs: path.join(projectPath, 'docs'),
      testCases: path.join(projectPath, 'test-cases'),
      code: path.join(projectPath, 'code')
    };
    
    // Create all folders
    await Promise.all([
      fs.mkdir(folders.slides, { recursive: true }),
      fs.mkdir(folders.mockups, { recursive: true }),
      fs.mkdir(folders.docs, { recursive: true }),
      fs.mkdir(folders.testCases, { recursive: true }),
      fs.mkdir(folders.code, { recursive: true })
    ]);
    
    // Create README files in each folder
    await Promise.all([
      createFolderReadme(folders.slides, 'Slides', 'Generated presentation slides using Reveal.js'),
      createFolderReadme(folders.mockups, 'Mockups', 'SVG wireframes and design mockups'),
      createFolderReadme(folders.docs, 'Documentation', 'Product documentation and specifications'),
      createFolderReadme(folders.testCases, 'Test Cases', 'Generated test files and test suites'),
      createFolderReadme(folders.code, 'Code', 'Generated project scaffold and source code')
    ]);
    
    logger.info('Project folder structure created:', { projectPath, folders: Object.keys(folders) });
    
    return { projectPath, folders };
  } catch (error) {
    logger.error('Failed to create project folders:', error);
    throw error;
  }
}

/**
 * Get project structure for an existing project
 */
export function getProjectStructure(projectPath: string): ProjectStructure {
  const folders: ProjectFolders = {
    slides: path.join(projectPath, 'slides'),
    mockups: path.join(projectPath, 'mockups'),
    docs: path.join(projectPath, 'docs'),
    testCases: path.join(projectPath, 'test-cases'),
    code: path.join(projectPath, 'code')
  };
  
  return { projectPath, folders };
}

/**
 * Check if project has Demo 1 folder structure
 */
export async function hasProjectStructure(projectPath: string): Promise<boolean> {
  try {
    const folders = ['slides', 'mockups', 'docs', 'test-cases', 'code'];
    const checks = await Promise.allSettled(
      folders.map(folder => fs.access(path.join(projectPath, folder)))
    );
    
    // Return true if at least 3 out of 5 folders exist
    const existingFolders = checks.filter(check => check.status === 'fulfilled').length;
    return existingFolders >= 3;
  } catch (error) {
    return false;
  }
}

/**
 * Generate a timestamped filename for generated assets
 */
export function generateTimestampedFilename(baseName: string, extension: string): string {
  const timestamp = new Date().toISOString()
    .replace(/[:.]/g, '-')
    .split('T')[0] + '_' + 
    new Date().toISOString()
    .split('T')[1]
    .substring(0, 8)
    .replace(/:/g, '-');
    
  return `${baseName}_${timestamp}.${extension}`;
}

/**
 * Save file to appropriate project folder
 */
export async function saveToProjectFolder(
  projectStructure: ProjectStructure,
  folderType: keyof ProjectFolders,
  filename: string,
  content: string
): Promise<string> {
  try {
    const folderPath = projectStructure.folders[folderType];
    const filePath = path.join(folderPath, filename);
    
    console.log(`🔧 [PROJECT-FOLDERS] Saving file to ${folderType} folder:`, {
      filename,
      folderPath,
      contentLength: content.length
    });
    
    // Ensure folder exists
    await fs.mkdir(folderPath, { recursive: true });
    
    // Write file
    await fs.writeFile(filePath, content, 'utf8');
    
    console.log(`🔧 [PROJECT-FOLDERS] File saved successfully:`, filePath);
    logger.info('File saved to project folder:', { folderType, filename, filePath });
    
    // Return relative path from project root
    const relativePath = path.relative(projectStructure.projectPath, filePath);
    console.log(`🔧 [PROJECT-FOLDERS] Relative path:`, relativePath);
    return relativePath;
  } catch (error) {
    console.error(`🔧 [PROJECT-FOLDERS] Failed to save file:`, error);
    logger.error('Failed to save file to project folder:', error);
    throw error;
  }
}

/**
 * Detect project root from current working directory or file path
 */
export async function detectProjectRoot(startPath?: string): Promise<string | null> {
  const searchPath = startPath || process.cwd();
  let currentPath = path.resolve(searchPath);
  
  // Look for common project indicators
  const projectIndicators = [
    'package.json',
    '.git',
    'tsconfig.json',
    'next.config.js',
    'vite.config.ts',
    'angular.json',
    'vue.config.js'
  ];
  
  // Search up the directory tree
  while (currentPath !== path.dirname(currentPath)) {
    try {
      const entries = await fs.readdir(currentPath);
      
      // Check if any project indicators exist
      const hasIndicator = projectIndicators.some(indicator => 
        entries.includes(indicator)
      );
      
      if (hasIndicator) {
        logger.info('Project root detected:', { projectRoot: currentPath });
        return currentPath;
      }
      
      currentPath = path.dirname(currentPath);
    } catch (error) {
      // Continue searching if directory is not accessible
      currentPath = path.dirname(currentPath);
    }
  }
  
  // Fallback to current working directory
  logger.warn('No project root detected, using current directory:', { fallback: process.cwd() });
  return process.cwd();
}

/**
 * Get or create project structure with auto-detection
 */
export async function getOrCreateProjectStructure(projectName?: string): Promise<ProjectStructure> {
  try {
    // Try to detect existing project root
    let projectRoot = await detectProjectRoot();
    
    // If we have a project name, create a subfolder
    if (projectName && projectRoot) {
      const sanitizedName = projectName.toLowerCase().replace(/[^a-z0-9-]/g, '-');
      projectRoot = path.join(projectRoot, `kapi-${sanitizedName}`);
    }
    
    if (!projectRoot) {
      // Fallback: create in current directory
      projectRoot = path.join(process.cwd(), projectName ? `kapi-${projectName}` : 'kapi-project');
    }
    
    // Create folder structure
    return await createProjectFolders(projectRoot);
  } catch (error) {
    logger.error('Failed to get or create project structure:', error);
    throw error;
  }
}

/**
 * Helper function to create README files in folders
 */
async function createFolderReadme(folderPath: string, title: string, description: string): Promise<void> {
  const readmeContent = `# ${title}

${description}

Generated by KAPI IDE Demo 1 workflow.

## Contents

This folder contains files generated automatically during the product development workflow.

---
*Last updated: ${new Date().toISOString()}*
`;
  
  const readmePath = path.join(folderPath, 'README.md');
  
  try {
    // Only create if README doesn't exist
    await fs.access(readmePath);
  } catch {
    // README doesn't exist, create it
    await fs.writeFile(readmePath, readmeContent, 'utf8');
  }
}