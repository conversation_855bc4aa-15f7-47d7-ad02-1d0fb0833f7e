import { Request, Response, NextFunction, RequestHandler } from 'express';

/**
 * Wrapper for async route handlers to properly catch errors
 * Express v5 has better async support, but this wrapper ensures compatibility
 * and provides better TypeScript types
 */
export const asyncHandler = <P = any, ResBody = any, ReqBody = any, ReqQuery = any>(
  fn: (
    req: Request<P, ResBody, ReqBody, ReqQuery>,
    res: Response<ResBody>,
    next: NextFunction,
  ) => Promise<void | any>,
): RequestHandler<P, ResBody, ReqBody, ReqQuery> => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Type-safe async route handler with proper error handling
 */
export type AsyncRequestHandler<P = any, ResBody = any, ReqBody = any, ReqQuery = any> = (
  req: Request<P, ResBody, ReqBody, ReqQuery>,
  res: Response<ResBody>,
  next: NextFunction,
) => Promise<void | Response<ResBody>>;

/**
 * Wrap async route handler with error handling
 */
export function catchAsync<P = any, ResBody = any, ReqBody = any, ReqQuery = any>(
  fn: AsyncRequestHandler<P, ResBody, ReqBody, ReqQuery>,
): RequestHandler<P, ResBody, ReqBody, ReqQuery> {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(statusCode: number, message: string, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Common API errors
 */
export const ApiErrors = {
  BadRequest: (message = 'Bad Request') => new ApiError(400, message),
  Unauthorized: (message = 'Unauthorized') => new ApiError(401, message),
  Forbidden: (message = 'Forbidden') => new ApiError(403, message),
  NotFound: (message = 'Not Found') => new ApiError(404, message),
  Conflict: (message = 'Conflict') => new ApiError(409, message),
  UnprocessableEntity: (message = 'Unprocessable Entity') => new ApiError(422, message),
  TooManyRequests: (message = 'Too Many Requests') => new ApiError(429, message),
  InternalServer: (message = 'Internal Server Error') => new ApiError(500, message, false),
  BadGateway: (message = 'Bad Gateway') => new ApiError(502, message, false),
  ServiceUnavailable: (message = 'Service Unavailable') => new ApiError(503, message, false),
};
